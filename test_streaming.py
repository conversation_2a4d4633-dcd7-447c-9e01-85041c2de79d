#!/usr/bin/env python3
"""
Test script to verify the streaming functionality of the Explain Answer feature.
This script tests the API endpoint and verifies that streaming works correctly.
"""

import requests
import time
import sys
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

def test_streaming_endpoint():
    """Test the streaming explain answer endpoint"""
    
    # Test URL - adjust as needed for your local setup
    base_url = "http://localhost:5000"  # Adjust if running on different port
    
    # You'll need to replace these with actual question_id and part_id from your database
    question_id = 1  # Replace with a valid question ID
    part_id = 1      # Replace with a valid part ID
    
    url = f"{base_url}/explain_answer/{question_id}/{part_id}"
    
    print(f"Testing streaming endpoint: {url}")
    print("=" * 50)
    
    try:
        # Make request with streaming enabled
        response = requests.get(url, stream=True, timeout=30)
        
        if response.status_code != 200:
            print(f"Error: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return False
        
        print("✅ Connection successful! Streaming response:")
        print("-" * 30)
        
        # Process streaming response
        full_response = ""
        chunk_count = 0
        
        for chunk in response.iter_content(chunk_size=1, decode_unicode=True):
            if chunk:
                full_response += chunk
                chunk_count += 1
                
                # Print each character as it comes in (simulating real-time display)
                print(chunk, end='', flush=True)
                
                # Small delay to simulate real streaming (optional)
                time.sleep(0.01)
        
        print("\n" + "-" * 30)
        print(f"✅ Streaming completed!")
        print(f"📊 Total chunks received: {chunk_count}")
        print(f"📝 Total response length: {len(full_response)} characters")
        
        # Test formatting rules
        print("\n🔍 Checking formatting compliance:")
        
        # Check for forbidden markdown formatting
        if "**" in full_response:
            print("⚠️  Warning: Found **bold** markdown formatting")
        else:
            print("✅ No **bold** markdown formatting found")
            
        if "*" in full_response and "**" not in full_response:
            # Check if it's just single asterisks (which might be bullet points)
            single_asterisks = [line for line in full_response.split('\n') if line.strip().startswith('*')]
            if single_asterisks:
                print("⚠️  Warning: Found *italic* or bullet point markdown formatting")
            else:
                print("✅ No problematic *italic* markdown formatting found")
        else:
            print("✅ No *italic* markdown formatting found")
            
        if "`" in full_response:
            print("⚠️  Warning: Found `code` markdown formatting")
        else:
            print("✅ No `code` markdown formatting found")
            
        # Check for proper heading format
        if full_response.count('#') > 0:
            print("✅ Found heading markers (#)")
        else:
            print("⚠️  No heading markers found")
            
        # Check for bullet points
        if "•" in full_response:
            print("✅ Found proper bullet points (•)")
        elif "*" in full_response:
            print("⚠️  Found asterisk bullet points instead of •")
        else:
            print("ℹ️  No bullet points found")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_prompt_formatting():
    """Test that our prompts are properly formatted"""
    
    print("\n🧪 Testing prompt formatting rules...")
    print("=" * 50)
    
    # Sample text that should be properly formatted by our frontend
    test_text = """# Key Concept
This is a test explanation with various formatting.

# Why Option A is Correct
Here are the reasons:
• First reason with bullet point
• Second reason with bullet point
1. Numbered point one
2. Numbered point two

Some **bold text** and *italic text* and `code text` should be handled.

This is a regular paragraph that should be wrapped properly.
"""
    
    print("Original text:")
    print(test_text)
    print("\n" + "-" * 30)
    
    # Simulate the frontend formatting
    formatted_text = test_text
    
    # Apply the same formatting rules as in the frontend
    formatted_text = (formatted_text
        .replace('# Key Concept', '<h3 class="text-lg font-semibold text-gray-900 mt-4 mb-2">Key Concept</h3>')
        .replace('# Why Option A is Correct', '<h3 class="text-lg font-semibold text-gray-900 mt-4 mb-2">Why Option A is Correct</h3>')
        .replace('• First reason with bullet point', '<li class="ml-4 list-disc list-inside mb-1">First reason with bullet point</li>')
        .replace('• Second reason with bullet point', '<li class="ml-4 list-disc list-inside mb-1">Second reason with bullet point</li>')
        .replace('1. Numbered point one', '<li class="ml-4 list-decimal list-inside mb-1">Numbered point one</li>')
        .replace('2. Numbered point two', '<li class="ml-4 list-decimal list-inside mb-1">Numbered point two</li>')
        .replace('**bold text**', '<strong class="font-semibold">bold text</strong>')
        .replace('*italic text*', '<em class="italic">italic text</em>')
        .replace('`code text`', '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm">code text</code>')
    )
    
    print("Formatted HTML:")
    print(formatted_text)
    print("\n✅ Frontend formatting test completed!")

if __name__ == "__main__":
    print("🚀 Testing Explain Answer Streaming Functionality")
    print("=" * 60)
    
    # Test prompt formatting first
    test_prompt_formatting()
    
    # Test streaming endpoint
    print("\n" + "=" * 60)
    success = test_streaming_endpoint()
    
    if success:
        print("\n🎉 All tests completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Tests failed!")
        sys.exit(1)
