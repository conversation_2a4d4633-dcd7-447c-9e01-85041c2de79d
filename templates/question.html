{% extends "base.html" %}
{% block head %}
{{ super() }}
<!-- Add required libraries for Markdown and LaTeX rendering -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css">
<script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/mathquill/0.10.1/mathquill.min.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.0/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/mathquill/0.10.1/mathquill.min.js"></script>
<!-- Include KaTeX for LaTeX rendering -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.4/dist/katex.min.css">
<script src="https://cdn.jsdelivr.net/npm/katex@0.16.4/dist/katex.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/katex@0.16.4/dist/contrib/auto-render.min.js"></script>

<!-- Include questionjs.js for question functionality -->
<script src="{{ url_for('static', filename='js/questionjs.js') }}"></script>

<!-- Initialize MathQuill -->
<script>
    // Initialize MathQuill interface
    var MQ = MathQuill.getInterface(2);
    var mathFields = {}; // Initialize mathFields as a global variable
</script>

<!-- Custom styles for LaTeX rendering and prose typography -->
<style>
    /* Ensure inline LaTeX is properly aligned with text */
    .katex-inline {
        display: inline-block;
        vertical-align: middle;
    }

    /* Add some spacing around inline LaTeX */
    .katex {
        margin: 0 0.1em;
    }

    /* Ensure display math has proper spacing */
    .katex-display {
        margin: 1em 0;
    }

    /* Make sure inline LaTeX doesn't break the line flow */
    p {
        display: block;
        line-height: 1.5;
        margin: 1em 0;
    }

    /* Custom prose styles for explanation content */
    .prose {
        color: #374151;
        max-width: none;
    }

    .prose h3 {
        color: #111827;
        font-weight: 600;
        font-size: 1.125rem;
        margin-top: 1.5rem;
        margin-bottom: 0.5rem;
        line-height: 1.6;
    }

    .prose h4 {
        color: #1f2937;
        font-weight: 600;
        font-size: 1rem;
        margin-top: 1.25rem;
        margin-bottom: 0.5rem;
        line-height: 1.5;
    }

    .prose h5 {
        color: #1f2937;
        font-weight: 600;
        font-size: 0.875rem;
        margin-top: 1rem;
        margin-bottom: 0.25rem;
        line-height: 1.4;
    }

    .prose p {
        margin-bottom: 0.5rem;
        line-height: 1.6;
    }

    .prose li {
        margin-left: 1rem;
        margin-bottom: 0.25rem;
        line-height: 1.6;
    }

    .prose ul {
        list-style-type: disc;
        margin-bottom: 1rem;
        padding-left: 1rem;
    }

    .prose ol {
        list-style-type: decimal;
        margin-bottom: 1rem;
        padding-left: 1rem;
    }

    .prose strong {
        font-weight: 600;
        color: #111827;
    }

    .prose em {
        font-style: italic;
    }

    .prose code {
        background-color: #f3f4f6;
        padding: 0.125rem 0.25rem;
        border-radius: 0.25rem;
        font-size: 0.875rem;
        font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
    }

    .prose blockquote {
        border-left: 4px solid #d1d5db;
        padding-left: 1rem;
        margin: 1rem 0;
        font-style: italic;
        color: #6b7280;
    }

    /* Ensure LaTeX content renders properly within prose */
    .prose .latex-content .katex {
        margin: 0 0.1em;
    }

    .prose .latex-content .katex-display {
        margin: 1em 0;
    }

    /* Smooth animations for explanation reveal */
    .explanation-container {
        transition: all 0.3s ease-in-out;
        transform-origin: top;
    }

    .explanation-container.hidden {
        opacity: 0;
        transform: scaleY(0);
        max-height: 0;
        overflow: hidden;
    }

    .explanation-container:not(.hidden) {
        opacity: 1;
        transform: scaleY(1);
        max-height: none;
    }

    /* Enhanced button hover effects */
    .explain-button {
        transition: all 0.2s ease-in-out;
        transform: translateY(0);
    }

    .explain-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
    }
</style>

    /* Ensure inline elements are properly aligned */
    .katex-html {
        display: inline-block;
        vertical-align: middle;
    }

    /* Style for our custom inline math wrapper */
    .inline-math {
        display: inline;
        vertical-align: baseline;
        margin: 0 0.1em;
    }

    /* Style for code blocks in markdown */
    pre {
        background-color: #f5f5f5;
        padding: 0.5em;
        border-radius: 0.25em;
        overflow-x: auto;
    }

    /* Style for inline code in markdown */
    code {
        background-color: #f5f5f5;
        padding: 0.2em 0.4em;
        border-radius: 0.25em;
        font-family: monospace;
    }

    /* Style for paragraphs in the preview */
    p {
        margin-bottom: 1em;
    }

    /* Style for headings in the preview */
    h1, h2, h3, h4, h5, h6 {
        margin-top: 1em;
        margin-bottom: 0.5em;
        font-weight: bold;
    }

    /* Style for lists in the preview */
    ul, ol {
        margin-left: 1.5em;
        margin-bottom: 1em;
    }

    /* SAQ Correct Answer Click-to-Toggle Styles */
    .saq-correct-answer-container {
        min-height: 2rem;
        cursor: pointer;
    }

    .saq-correct-answer-hidden {
        transition: filter 0.3s ease-in-out;
        filter: blur(4px); /* Default blurred state */
    }

    .saq-hover-hint {
        backdrop-filter: blur(2px);
        border: 1px dashed #d1d5db;
        transition: opacity 0.3s ease-in-out;
    }

    /* SAQ Feedback Click-to-Toggle Styles */
    .saq-feedback-container {
        min-height: 2rem;
        cursor: pointer;
    }

    .saq-feedback-hidden {
        transition: filter 0.3s ease-in-out;
        filter: blur(4px); /* Default blurred state */
    }

    /* Toggle states for click functionality */
    .saq-correct-answer-container.revealed .saq-correct-answer-hidden {
        filter: blur(0px) !important;
    }

    .saq-correct-answer-container.revealed .saq-hover-hint {
        opacity: 0 !important;
    }

    .saq-feedback-container.revealed .saq-feedback-hidden {
        filter: blur(0px) !important;
    }

    .saq-feedback-container.revealed .saq-hover-hint {
        opacity: 0 !important;
    }

    /* Add subtle animation to the eye icon */
    .saq-hover-hint svg {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.7;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Add MathQuill dependencies in the head -->


<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <div class="grid grid-cols-12 gap-6">
        <!-- Left Column: Current Question -->
        <div class="col-span-9">
            {% if feedback != None %}
                <div id="feedback-container" class="mb-6 bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden">
                    <div class="p-6 prose prose-sm max-w-none">{{ feedback | safe }}</div>
                </div>
            {% endif %}

            <!-- Question Card -->
            <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden mb-6 min-h-[100px]">
                <div class="p-6">
                    <div class="flex justify-between items-start mb-3">
                        <h2 class="text-lg font-semibold text-gray-900">#{{ question.id }} [{{ question.source}}] {{ question.title }}</h2>
                        {% if session.user_id %}
                            {% set user = User.query.get(session['user_id']) %}
                            {% if user and user.is_admin %}
                            <div class="flex flex-col space-y-2">
                                <a href="{{ url_for('edit_question', question_id=question.id) }}"
                                   class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    <i class="fas fa-edit mr-1.5"></i> Edit Question
                                </a>
                                <a href="#"
                                    onclick="confirmDelete({{ question.id }})"
                                    class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                    <i class="fas fa-trash mr-1.5"></i> Delete Question
                                </a>
                            </div>
                            {% endif %}
                        {% endif %}
                    </div>
                    <p class="text-sm text-gray-500 mb-4">
                        <span class="font-medium">Topic:</span>
                        {% if question.topic %}
                            {{ question.topic.name }}
                        {% else %}
                            <span class="text-gray-400">No topic</span>
                        {% endif %}
                    </p>

                    <!-- {% if question.attachment != None %}
                    <div class="mb-4">
                        <img src="{{ url_for('serve.serve_file', filename=question.attachment) }}"
                             class="rounded-lg max-h-[600px] w-auto mx-auto"
                             alt="Question attachment">
                    </div>
                    {% endif %} -->

                    <p class="text-gray-700">
                        <!-- {% if question.description != None %}
                            {{ question.description | safe }}
                        {% endif %} -->
                        <span class="text-sm text-gray-500">
                            [{% set total_score = question.parts | sum(attribute='score') %}{{ "%.1f"|format(total_score) if total_score != 0 else 'N/A' }}m]
                        </span>
                    </p>
                </div>
            </div>
            <!-- Parts -->
            {% for part in question.parts %}
            <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden mb-6">
                <div class="p-6">
                    <div class="text-md text-gray-900 mb-4">
                        <div class="part-description" data-content="{{ part.description }}"></div>
                        <span class="text-sm text-gray-500">[{{ part.score }}m]</span>
                    </div>

                    {% if part.attachments != None %}
                    <div class="mb-4">
                        {% for attachment in part.attachments %}
                        <img src="{{ url_for('serve.serve_file', filename=attachment.filename) }}"
                             class="rounded-lg max-h-[400px] w-auto mx-auto"
                             alt="Part attachment">
                        {% endfor %}
                    </div>
                    {% endif %}

                    <!-- Past Submissions Section -->
                    {% if part.submissions and part.submissions|length > 0 %}
                    <div class="mb-6 bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-sm font-medium text-gray-900">Your Past Submissions</h4>
                            <button data-part-id="{{ part.id }}" class="toggle-submissions text-xs text-indigo-600 hover:text-indigo-500 transition-colors duration-200">
                                Show All
                            </button>
                        </div>
                        <div id="submissions-{{ part.id }}" class="space-y-2 hidden">
                            {% for submission in part.submissions|sort(attribute='timestamp', reverse=true) %}
                                {% if submission.user_id == session.get('user_id') %}
                                <a href="{{ url_for('submission_details', submission_id=submission.id) }}"
                                   class="block bg-white p-3 rounded-lg border border-gray-200 hover:border-indigo-200 hover:bg-indigo-50/50 transition-all duration-200">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <span class="flex h-2 w-2 rounded-full {% if submission.score == part.score %}bg-green-500{% elif submission.score > 0 %}bg-yellow-500{% else %}bg-red-500{% endif %}"></span>
                                            <span class="text-sm font-medium text-gray-900">{% if submission.score == part.score %}Correct{% elif submission.score > 0 %}Partial{% else %}Incorrect{% endif %}</span>
                                        </div>
                                        <span class="text-xs text-gray-500">{{ submission.timestamp.strftime('%b %d, %H:%M') }}</span>
                                    </div>
                                    <div class="mt-2 flex items-center justify-between">
                                        <div class="text-sm text-gray-600">Score: {{ submission.score }}</div>
                                        <span class="text-xs text-indigo-600">View Details →</span>
                                    </div>
                                </a>
                                {% endif %}
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    {%if part.input_type == 'mcq'%}
                    <form
                        method="post"
                        action="{{ url_for('get_git_diff', question_id=question.id, part_id=part.id) }}"
                        onsubmit="return submitAnswer(event, {{ part.id }}, {{ question.id }});"
                        class="space-y-4"
                        data-part-id="{{ part.id }}"
                        data-input-type="{{ 'mcq' }}"
                    >
                    {%else%}
                     <form
                        method="post"
                        action="{{ url_for('get_git_diff', question_id=question.id, part_id=part.id) }}"
                        onsubmit="return submitAnswer(event, {{ part.id }}, {{ question.id }});"
                        class="space-y-4"
                        data-part-id="{{ part.id }}"
                        data-input-type="{{ 'saq' }}"
                    >
                    {%endif%}
                        <!-- SAQ Input (default) -->
                        {% if not part.input_type == 'mcq' %}
                        <div class="saq-input">
                            <label for="answer_{{ part.id }}" class="block text-sm font-medium text-gray-700 mb-1"></label>
                            <div class="space-y-4">
                                <textarea
                                    class="block w-full rounded-md border-0 py-3 px-4 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    id="answer_{{ part.id }}"
                                    name="answer"
                                    rows="4"
                                    placeholder="Enter your answer here..."></textarea>

                                <!-- Image Upload Section -->
                                <div class="flex items-center space-x-4">
                                    <label for="image_{{ part.id }}" class="flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer">
                                        <i class="fas fa-camera mr-2"></i>
                                        Upload Image
                                        <input type="file"
                                               id="image_{{ part.id }}"
                                               name="image"
                                               accept="image/png,image/jpeg,image/jpg"
                                               class="hidden"
                                               onchange="previewImage(this, 'preview_{{ part.id }}')">
                                    </label>
                                    <div id="preview_{{ part.id }}" class="hidden">
                                        <img src="" alt="Preview" class="max-h-32 rounded-lg shadow-sm">
                                        <button type="button"
                                                onclick="removeImage('image_{{ part.id }}', 'preview_{{ part.id }}')"
                                                class="ml-2 text-red-600 hover:text-red-800">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% else%}
                        <!-- MCQ Input -->
                        <div class="mcq-input">
                            <fieldset>
                                <legend class="block text-sm font-medium text-gray-700 mb-3">Select the correct answer:</legend>
                                <div class="space-y-3" id="mcq_options_{{ part.id }}">
                                    {% for option in part.options %}
                                    <div class="mcq-option relative flex items-start">
                                        <div class="flex items-center h-5">
                                            <input
                                                id="mcq_option_{{ part.id }}_{{ loop.index0 }}"
                                                name="answer"
                                                type="radio"
                                                value="{{ loop.index0 }}"
                                                class="h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500"
                                            >
                                        </div>
                                        <div class="ml-3 text-sm">
                                            <label for="mcq_option_{{ part.id }}_{{ loop.index0 }}" class="font-medium text-gray-700">
                                                <div class="option-content">{{ option.description | safe }}</div>
                                            </label>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </fieldset>
                        </div>
                        {% endif %}

                        <!-- Calculator toggle button -->
                        <div class="flex justify-end mb-2">
                            <button type="button"
                                    onclick="toggleCalculator({{ part.id }})"
                                    class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-indigo-600 hover:text-indigo-500">
                                <svg class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 2a8 8 0 100 16 8 8 0 000-16zM5.5 9a1.5 1.5 0 100-3 1.5 1.5 0 000 3zm9 0a1.5 1.5 0 100-3 1.5 1.5 0 000 3zm-9 3a1.5 1.5 0 100 3 1.5 1.5 0 000-3zm9 0a1.5 1.5 0 100 3 1.5 1.5 0 000-3z" clip-rule="evenodd"/>
                                </svg>
                                <span id="calc_toggle_text_{{ part.id }}">Show Calculator</span>
                            </button>
                        </div>

                        <!-- Calculator interface (hidden by default) -->
                        <div id="calculator_{{ part.id }}" class="hidden">
                            <!-- MathQuill input field -->
                            <div class="mb-4">
                                <div id="math_input_{{ part.id }}" class="border rounded-md p-2 min-h-[60px] focus-within:ring-2 focus-within:ring-indigo-600 focus-within:border-indigo-600"></div>
                            </div>
                            <!-- Calculator buttons -->
                            <div class="grid grid-cols-4 gap-2 max-w-md mx-auto mb-4">
                                <!-- Basic operators -->
                                <button type="button" onclick="insertMath({{ part.id }}, '+')" class="calc-btn">+</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '-')" class="calc-btn">−</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '\\times')" class="calc-btn">×</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '\\div')" class="calc-btn">÷</button>

                                <!-- Numbers -->
                                <button type="button" onclick="insertMath({{ part.id }}, '7')" class="calc-btn">7</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '8')" class="calc-btn">8</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '9')" class="calc-btn">9</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '(')" class="calc-btn">(</button>

                                <button type="button" onclick="insertMath({{ part.id }}, '4')" class="calc-btn">4</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '5')" class="calc-btn">5</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '6')" class="calc-btn">6</button>
                                <button type="button" onclick="insertMath({{ part.id }}, ')')" class="calc-btn">)</button>

                                <button type="button" onclick="insertMath({{ part.id }}, '1')" class="calc-btn">1</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '2')" class="calc-btn">2</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '3')" class="calc-btn">3</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '^')" class="calc-btn">^</button>

                                <button type="button" onclick="insertMath({{ part.id }}, '0')" class="calc-btn">0</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '.')" class="calc-btn">.</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '\\sqrt')" class="calc-btn">√</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '\\frac')" class="calc-btn">⅟</button>

                                <!-- Advanced functions -->
                                <button type="button" onclick="insertMath({{ part.id }}, '\\sin')" class="calc-btn">sin</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '\\cos')" class="calc-btn">cos</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '\\tan')" class="calc-btn">tan</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '\\pi')" class="calc-btn">π</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '\\ln')" class="calc-btn">ln</button>

                            </div>
                            <!-- Insert button -->
                            <div class="flex justify-end mb-4">
                                <button type="button"
                                        onclick="insertToTextarea({{ part.id }})"
                                        class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-500 rounded-md">
                                    Insert Expression into Answer
                                </button>
                            </div>
                        </div>

                        <div class="flex justify-end">
                            <button
                                type="submit"
                                class="inline-flex justify-center py-2.5 px-5 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                                id="submit-part-{{ part.id }}"
                            >
                                Submit Part {{ loop.index }}
                            </button>
                        </div>

                        <!-- Loading indicator -->
                        <div id="loading_{{ part.id }}" class="hidden">
                            <div class="flex flex-col items-center justify-center py-4">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-t-2 border-indigo-600 mb-2"></div>
                                <p class="text-sm text-gray-600">Processing your answer...</p>
                            </div>
                        </div>

                        <!-- Feedback panels -->
                        <div id="answer_panels_{{ part.id }}" class="hidden mt-6">
                            <div class="grid grid-cols-2 gap-6">
                                <div class="p-5 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                                    <h4 class="text-sm font-medium text-gray-900 mb-3">Your Answer</h4>
                                    <div id="user_answer_{{ part.id }}" class="text-sm text-gray-700 space-y-3"></div>
                                </div>
                                <div class="p-5 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                                    <h4 class="text-sm font-medium text-gray-900 mb-3">Marking Points</h4>
                                    <div id="marking_scheme_{{ part.id }}" class="text-sm text-gray-700 space-y-3"></div>
                                </div>
                            </div>

                            <!-- Explain Answer Button -->
                            <div class="mt-4 flex justify-center">
                                <button type="button" onclick="explainAnswer({{ part.id }}, {{ question.id }})" class="explain-button inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    <i class="fas fa-lightbulb mr-1.5"></i> Explain Answer
                                </button>
                            </div>

                            <!-- Explanation Section -->
                            <div id="explanation-{{ part.id }}" class="explanation-container mt-4 hidden">
                                <div class="p-5 bg-white rounded-lg border border-gray-200 shadow-sm">
                                    <div class="flex items-center justify-between mb-3">
                                        <h4 class="text-sm font-semibold text-gray-900">Key Concepts & Explanation (Based off RI notes)</h4>
                                        <div class="explanation-loading-{{ part.id }} hidden">
                                            <div class="w-5 h-5 border-2 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
                                        </div>
                                    </div>
                                    <div class="explanation-content-{{ part.id }} prose prose-sm max-w-none text-gray-700 latex-content leading-relaxed">
                                        <!-- Explanation content will be loaded here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            {% endfor %}

            <!-- Submit All Button -->
            <div class="mt-8 mb-6">
                <button
                    type="button"
                    onclick="console.log('Submit All button clicked'); if(typeof submitAllAnswers === 'function') { submitAllAnswers(event); } else { alert('Function not found'); }"
                    class="w-full flex justify-center py-3 px-6 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                    id="submit-all-button"
                >
                    Submit All Answers
                </button>
            </div>

            <!-- Debug information -->
            <div class="mt-4 p-4 bg-gray-100 rounded-lg text-xs text-gray-700" id="debug-info">
                <p>If the Submit All button doesn't work, please check the browser console for errors (F12).</p>
                <p class="mt-2">You can also try submitting each part individually using the "Submit Part" buttons.</p>
            </div>

            <div class="mt-6 text-center">
                <a href="{{ url_for('vault') }}" class="inline-flex items-center text-sm font-semibold leading-6 text-gray-900 hover:text-gray-700">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Vault
                </a>
            </div>
        </div>

        <!-- Right Column: Review and Similar Problems -->
        <div class="col-span-3">
            <!-- Review Section -->
            <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden mb-6">
                <div class="p-4">
                    <h3 class="text-sm font-semibold text-gray-900 mb-3">Review</h3>
                    <div class="space-y-2 overflow-y-auto max-h-[calc(50vh-4rem)]">
                        {% if review_problems %}
                            {% for problem in review_problems %}
                            <a href="{{ url_for('load_question', question_id=problem.question_id) }}"
                               class="block p-3 rounded-lg border border-gray-200 hover:border-indigo-200 hover:bg-indigo-50/50 transition-all duration-200">
                                <div class="text-sm font-medium text-gray-900">
                                    {% if problem.topic %}
                                        {{ problem.topic.name }}
                                    {% else %}
                                        <span class="text-gray-400">No topic</span>
                                    {% endif %}
                                </div>
                                <div class="mt-2 flex items-center space-x-2">
                                    <span class="flex h-2 w-2 rounded-full {% if problem.score == problem.max_score %}bg-green-500{% elif problem.score > 0 %}bg-yellow-500{% else %}bg-red-500{% endif %}"></span>
                                    <span class="text-xs text-gray-600">{{ problem.score|round|int }}/{{ problem.max_score|round|int }}</span>
                                </div>
                            </a>
                            {% endfor %}
                        {% else %}
                            <div class="text-sm text-gray-500">You're all caught up!</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Similar Problems Section -->
            <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden">
                <div class="p-4">
                    <h3 class="text-sm font-semibold text-gray-900 mb-3">Similar Problems</h3>
                    <div class="space-y-2 overflow-y-auto max-h-[calc(50vh-4rem)]">
                        {% for similar in similar_problems %}
                        <a href="{{ url_for('load_question', question_id=similar.id) }}"
                           class="block p-3 rounded-lg border border-gray-200 hover:border-indigo-200 hover:bg-indigo-50/50 transition-all duration-200 relative">
                            <div class="flex items-center">
                                <div class="flex-grow">
                                    <div class="text-sm font-medium text-gray-900">{{ similar.source }}</div>
                                    <div class="text-xs text-gray-500 mt-1">
                                        {% if similar.topic %}
                                            {{ similar.topic.name }}
                                        {% else %}
                                            <span class="text-gray-400">No topic</span>
                                        {% endif %}
                                    </div>
                                    <div class="text-xs text-gray-500 mt-2 line-clamp-2">{{ similar.description }}</div>
                                </div>
                                <div class="ml-3">
                                    <span class="flex h-2.5 w-2.5 rounded-full {% if similar.status == 'not_attempted' %}bg-gray-200{% elif similar.status == 'correct' %}bg-green-500{% elif similar.status == 'partial' %}bg-yellow-500{% else %}bg-red-500{% endif %}"></span>
                                </div>
                            </div>
                        </a>
                        {% else %}
                        <div class="text-sm text-gray-500">No similar problems found</div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<script>
    // Global function to render LaTeX in any element
    function renderLatexInElement(element) {
        console.log('renderLatexInElement called with:', element);

        if (!element) {
            console.warn('Element not provided to renderLatexInElement');
            return;
        }

        if (typeof renderMathInElement === 'undefined') {
            console.warn('renderMathInElement not available - KaTeX auto-render not loaded');
            return;
        }

        try {
            console.log('Rendering LaTeX in element:', element.id || element.className);
            renderMathInElement(element, {
                delimiters: [
                    {left: '$$', right: '$$', display: true},
                    {left: '$', right: '$', display: false},
                    {left: '\\(', right: '\\)', display: false},
                    {left: '\\[', right: '\\]', display: true}
                ],
                throwOnError: false,
                output: 'html'
            });
            console.log('LaTeX rendering completed for element:', element.id || element.className);
        } catch (error) {
            console.error('Error rendering LaTeX:', error);
        }
    }

    // Global function to render LaTeX in multiple elements
    function renderLatexInElements(selector) {
        console.log('renderLatexInElements called with selector:', selector);
        const elements = document.querySelectorAll(selector);
        console.log('Found elements:', elements.length);
        elements.forEach(element => renderLatexInElement(element));
    }

    window.addEventListener('load', function () {
        // Test LaTeX rendering availability
        console.log('Page loaded, testing LaTeX rendering...');
        console.log('renderMathInElement available:', typeof renderMathInElement !== 'undefined');
        console.log('katex available:', typeof katex !== 'undefined');

        // Test with a simple element
        setTimeout(() => {
            const testDiv = document.createElement('div');
            testDiv.innerHTML = 'Test LaTeX: $x^2 + y^2 = z^2$';
            document.body.appendChild(testDiv);
            renderLatexInElement(testDiv);
            console.log('Test div after LaTeX rendering:', testDiv.innerHTML);
            document.body.removeChild(testDiv);
        }, 500);
        console.log('DOM fully loaded');

        // Check if required libraries are loaded
        if (typeof marked === 'undefined') {
            console.error('Marked library is not loaded.');
            return;
        }

        if (typeof katex === 'undefined') {
            console.error('KaTeX library is not loaded.');
            return;
        }

        console.log('Libraries loaded successfully');

        // Additional marked configuration
        marked.use({
            breaks: true,  // Convert line breaks to <br>
            gfm: true,    // Enable GitHub Flavored Markdown
            mangle: false, // Don't mangle email addresses
            headerIds: false // Don't add IDs to headers
        });

        // Function to render LaTeX with KaTeX
        function renderLatex(latex, displayMode) {
            try {
                // Render the LaTeX expression
                const rendered = katex.renderToString(latex, {
                    displayMode: displayMode,
                    throwOnError: false,
                    output: 'html'
                });

                // For inline LaTeX, add a special class to help with styling
                if (!displayMode) {
                    return `<span class="inline-math">${rendered}</span>`;
                }

                return rendered;
            } catch (error) {
                console.error('Error rendering LaTeX:', error, latex);
                return `<span class="text-red-500">Error rendering LaTeX: ${latex}</span>`;
            }
        }



        // Custom function to handle LaTeX in text
        function processLatexInText(text) {
            if (!text) return '';

            // First, escape HTML characters in the text
            let processedText = text
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#039;');

            // Replace display math ($$...$$)
            processedText = processedText.replace(/\$\$(.*?)\$\$/g, function (match, latex) {
                return renderLatex(latex.trim(), true);
            });

            // Replace inline math ($...$)
            processedText = processedText.replace(/\$([^\$]+?)\$/g, function (match, latex) {
                return renderLatex(latex.trim(), false);
            });

            return processedText;
        }

        // Process text containing both Markdown and LaTeX
        function processText(text) {
            if (!text) return '';

            console.log('Processing text:', text);

            try {
                // Our approach: Process LaTeX first, then Markdown

                // Step 1: Extract and save LaTeX blocks
                let placeholders = [];
                let processedText = text;

                // Extract display math ($$...$$)
                processedText = processedText.replace(/\$\$(.*?)\$\$/gs, function (match, latex) {
                    const id = `latex-${Math.random().toString(36).substring(2, 10)}`;
                    placeholders.push({
                        id: id,
                        latex: latex.trim(),
                        displayMode: true
                    });
                    return `<span id="${id}" class="latex-placeholder"></span>`;
                });

                // Extract inline math ($...$)
                processedText = processedText.replace(/\$([^\$\n]+?)\$/g, function (match, latex) {
                    const id = `latex-${Math.random().toString(36).substring(2, 10)}`;
                    placeholders.push({
                        id: id,
                        latex: latex.trim(),
                        displayMode: false
                    });
                    return `<span id="${id}" class="latex-placeholder"></span>`;
                });

                // Step 2: Parse the text as Markdown
                let html = marked.parse(processedText);

                // Step 3: Replace placeholders with rendered LaTeX
                placeholders.forEach(placeholder => {
                    const rendered = renderLatex(placeholder.latex, placeholder.displayMode);
                    html = html.replace(
                        new RegExp(`<span id="${placeholder.id}" class="latex-placeholder"></span>`, 'g'),
                        rendered
                    );
                });

                return html;
            } catch (error) {
                console.error('Error in custom processing:', error);

                // Fallback to simpler approach
                try {
                    console.log('Using simpler fallback approach');

                    // Process LaTeX directly
                    const processedWithLatex = processLatexInText(text);

                    // Wrap in a div
                    return `<div>${processedWithLatex}</div>`;
                } catch (fallbackError) {
                    console.error('Fallback approach failed:', fallbackError);

                    // Last resort: just escape the text
                    try {
                        console.log('Using basic text escaping');
                        const escaped = text
                            .replace(/&/g, '&amp;')
                            .replace(/</g, '&lt;')
                            .replace(/>/g, '&gt;')
                            .replace(/"/g, '&quot;')
                            .replace(/'/g, '&#039;');

                        return `<p>${escaped}</p>`;
                    } catch (lastResortError) {
                        console.error('Even basic escaping failed:', lastResortError);
                        return `<p class="text-red-500">Error processing text</p><pre>${text}</pre>`;
                    }
                }
            }
        }

        // Function to update the preview
        function updatePreview(textarea) {
            // Find the preview element (it's the next sibling with class 'live-preview')
            let previewElement = null;
            let currentElement = textarea.nextElementSibling;

            while (currentElement) {
                if (currentElement.classList.contains('live-preview')) {
                    previewElement = currentElement;
                    break;
                }
                currentElement = currentElement.nextElementSibling;
            }

            if (!previewElement) {
                console.error('Preview element not found for textarea:', textarea);
                return;
            }

            const text = textarea.value || '';

            // Process the text and update the preview
            const processedHTML = processText(text);
            previewElement.innerHTML = processedHTML || '<p class="text-gray-400 italic">Preview will appear here</p>';
        }

        // Find all textareas with the latex-content class
        const textareas = document.querySelectorAll('.latex-content');
        console.log('Found textareas:', textareas.length);

        // Initialize each textarea
        textareas.forEach(textarea => {
            console.log('Initializing textarea:', textarea.id);
            // Initial render
            updatePreview(textarea);

            // Add input event listener
            textarea.addEventListener('input', function() {
                updatePreview(this);
            });
        });

        // Render part descriptions
        const partDescriptions = document.querySelectorAll('.part-description');
        console.log('Found part descriptions:', partDescriptions.length);

        partDescriptions.forEach(element => {
            const content = element.getAttribute('data-content');
            if (content) {
                const processedHTML = processText(content);
                element.innerHTML = processedHTML;
            }
        });

        // Render MCQ option content
        const mcqOptions = document.querySelectorAll('.option-content');
        console.log('Found MCQ options:', mcqOptions.length);

        mcqOptions.forEach(element => {
            const content = element.textContent;
            if (content) {
                const processedHTML = processText(content);
                element.innerHTML = processedHTML;
            }
        });

        // Render feedback
        const feedbackContainer = document.getElementById('feedback-container');
        if (feedbackContainer) {
            const feedbackDiv = feedbackContainer.querySelector('.prose');
            const content = feedbackDiv.innerHTML;
            if (content) {
                const processedHTML = processText(content);
                feedbackDiv.innerHTML = processedHTML;
            }
        }
    });


    function showLoading(event, partId) {
        event.preventDefault();
        const form = event.target;
        const loading = document.getElementById(`loading_${partId}`);
        const correctAnswer = document.getElementById(`correct_answer_${partId}`);

        loading.classList.remove('hidden');
        correctAnswer.classList.remove('hidden');

        return false; // Prevent form submission as we're using submitAllAnswers
    }

    document.addEventListener('DOMContentLoaded', function() {
        // MathQuill initialization is now handled in questionjs.js

        // Add toggle handlers for submissions
        document.querySelectorAll('.toggle-submissions').forEach(button => {
            button.addEventListener('click', function() {
                const partId = this.getAttribute('data-part-id');
                const submissionsList = document.getElementById(`submissions-${partId}`);

                if (submissionsList.classList.contains('hidden')) {
                    submissionsList.classList.remove('hidden');
                    this.textContent = 'Hide';
                } else {
                    submissionsList.classList.add('hidden');
                    this.textContent = 'Show All';
                }
            });
        });

        // Add event listener to save cursor position when clicking in textarea
        document.querySelectorAll('textarea[id^="answer_"]').forEach(textarea => {
            textarea.addEventListener('click', function() {
                const partId = this.id.split('_')[1];
                const calculator = document.getElementById(`calculator_${partId}`);
                if (calculator) {
                    calculator.dataset.cursorPosition = this.selectionStart;
                }
            });

            textarea.addEventListener('keyup', function() {
                const partId = this.id.split('_')[1];
                const calculator = document.getElementById(`calculator_${partId}`);
                if (calculator) {
                    calculator.dataset.cursorPosition = this.selectionStart;
                }
            });
        });
    });

    // Function to insert math symbols
    function insertMath(partId, symbol) {
        const mathField = mathFields[partId];
        if (mathField) {
            if (symbol === '\\sqrt' || symbol === '\\frac' ) {
                mathField.cmd(symbol);
            } else if (symbol === '^') {
                mathField.cmd('^');
                mathField.typedText('');
            } else {
                mathField.write(symbol);
            }
            mathField.focus();
        }
    }

    // Function to insert to textarea at cursor position
    function insertToTextarea(partId) {
        const mathField = mathFields[partId];
        const textarea = document.getElementById(`answer_${partId}`);

        if (mathField && textarea) {
            const latex = mathField.latex();
            if (!latex) return; // Don't insert if empty

            // Save the current cursor position or use the end of text
            const savedStart = textarea.selectionStart || textarea.value.length;
            const savedEnd = textarea.selectionEnd || textarea.value.length;

            // Format the LaTeX for display
            let displayText = latex;
            // If it's a simple expression (no special LaTeX commands), keep it as is
            if (!latex.includes('\\')) {
                displayText = latex;
            } else {
                // Wrap LaTeX expressions in $$ for proper display
                displayText = `$${latex}$`;
            }

            // Insert at cursor position or append to end
            const textBefore = textarea.value.substring(0, savedStart);
            const textAfter = textarea.value.substring(savedEnd);

            // Add spaces around the expression if needed
            const needSpaceBefore = textBefore.length > 0 && !textBefore.endsWith(' ');
            const needSpaceAfter = textAfter.length > 0 && !textAfter.startsWith(' ');

            const newText = textBefore +
                          (needSpaceBefore ? ' ' : '') +
                          displayText +
                          (needSpaceAfter ? ' ' : '') +
                          textAfter;

            // Update textarea while preserving existing content
            textarea.value = newText;

            // Calculate and restore cursor position after the inserted expression
            const newCursorPos = savedStart +
                               (needSpaceBefore ? 1 : 0) +
                               displayText.length +
                               (needSpaceAfter ? 1 : 0);

            textarea.focus();
            textarea.selectionStart = newCursorPos;
            textarea.selectionEnd = newCursorPos;

            // Clear the math input for next expression
            mathField.latex('');
        }
    }

    // Function to toggle calculator
    function toggleCalculator(partId) {
        const calculator = document.getElementById(`calculator_${partId}`);
        const toggleText = document.getElementById(`calc_toggle_text_${partId}`);
        const textarea = document.getElementById(`answer_${partId}`);

        if (calculator.classList.contains('hidden')) {
            calculator.classList.remove('hidden');
            toggleText.textContent = 'Hide Calculator';

            // Save cursor position when opening calculator
            calculator.dataset.cursorPosition = textarea.selectionStart || textarea.value.length;
        } else {
            calculator.classList.add('hidden');
            toggleText.textContent = 'Show Calculator';

            // Restore focus to textarea when closing calculator
            textarea.focus();
            const savedPos = parseInt(calculator.dataset.cursorPosition) || textarea.value.length;
            textarea.selectionStart = savedPos;
            textarea.selectionEnd = savedPos;
        }
    }

    function previewImage(input, previewId) {
        const preview = document.getElementById(previewId);
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.querySelector('img').src = e.target.result;
                preview.classList.remove('hidden');
            }
            reader.readAsDataURL(input.files[0]);
        }
    }

    function toggleTimingDetails(partId, event) {
        // Prevent form submission
        if (event) {
            event.preventDefault();
            event.stopPropagation();
        }

        // Try MCQ, SAQ individual, and SAQ bulk timing detail IDs
        const mcqDetailsDiv = document.getElementById(`timing-details-mcq-${partId}`);
        const saqDetailsDiv = document.getElementById(`timing-details-${partId}`);
        const saqIndividualDetailsDiv = document.getElementById(`timing-details-saq-${partId}`);
        const mcqToggleSpan = document.getElementById(`timing-toggle-mcq-${partId}`);
        const saqToggleSpan = document.getElementById(`timing-toggle-${partId}`);
        const saqIndividualToggleSpan = document.getElementById(`timing-toggle-saq-${partId}`);

        const detailsDiv = mcqDetailsDiv || saqDetailsDiv || saqIndividualDetailsDiv;
        const toggleSpan = mcqToggleSpan || saqToggleSpan || saqIndividualToggleSpan;

        if (detailsDiv && toggleSpan) {
            const currentText = toggleSpan.textContent;
            if (detailsDiv.classList.contains('hidden')) {
                detailsDiv.classList.remove('hidden');
                // Extract timing from current text and show "Hide"
                const timingMatch = currentText.match(/⏱ (\d+ms)/);
                if (timingMatch) {
                    toggleSpan.textContent = '▼ ' + timingMatch[1];
                } else {
                    toggleSpan.textContent = 'Hide Details';
                }
            } else {
                detailsDiv.classList.add('hidden');
                // Extract timing from current text and show "Show"
                const timingMatch = currentText.match(/(\d+ms)/);
                if (timingMatch) {
                    toggleSpan.textContent = '⏱ ' + timingMatch[1];
                } else {
                    toggleSpan.textContent = 'Show Details';
                }
            }
        }

        return false;
    }

    function removeImage(inputId, previewId) {
        document.getElementById(inputId).value = '';
        document.getElementById(previewId).classList.add('hidden');
    }

    // Function to explain the answer using Gemini LLM
    async function explainAnswer(partId, questionId) {
        const explanationDiv = document.getElementById(`explanation-${partId}`);
        const explanationContent = document.querySelector(`.explanation-content-${partId}`);
        const loadingIndicator = document.querySelector(`.explanation-loading-${partId}`);

        if (!explanationDiv || !explanationContent || !loadingIndicator) {
            console.error('Required explanation elements not found');
            return;
        }

        // If already visible, toggle it off and return
        if (!explanationDiv.classList.contains('hidden')) {
            explanationDiv.classList.add('hidden');
            return;
        }

        // Show the explanation div and loading indicator
        explanationDiv.classList.remove('hidden');
        loadingIndicator.classList.remove('hidden');
        explanationContent.innerHTML = '<div class="text-gray-500 text-sm italic">Generating explanation...</div>';

        try {
            // Fetch the explanation from the server
            const response = await fetch(`/explain_answer/${questionId}/${partId}`, {
                method: 'GET'
            });

            if (!response.ok) {
                throw new Error(`HTTP error ${response.status}`);
            }

            // Set up SSE for streaming response
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let explanation = '';

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value, { stream: true });
                explanation += chunk;

                // Apply proper formatting to improve readability
                let formattedExplanation = explanation
                    // Add proper styling to headings
                    .replace(/^# (.*?)$/gm, '<h3 class="text-lg font-semibold text-gray-900 mt-4 mb-2">$1</h3>')
                    .replace(/^## (.*?)$/gm, '<h4 class="text-md font-semibold text-gray-800 mt-3 mb-2">$1</h4>')
                    .replace(/^### (.*?)$/gm, '<h5 class="text-sm font-semibold text-gray-800 mt-2 mb-1">$1</h5>')
                    // Style bullet points with • symbol
                    .replace(/^• (.*?)$/gm, '<li class="ml-4 list-disc list-inside mb-1">$1</li>')
                    // Style numbered lists
                    .replace(/^\d+\. (.*?)$/gm, '<li class="ml-4 list-decimal list-inside mb-1">$1</li>')
                    // Style traditional markdown lists (fallback)
                    .replace(/^\* (.*?)$/gm, '<li class="ml-4 list-disc list-inside mb-1">$1</li>')
                    .replace(/^- (.*?)$/gm, '<li class="ml-4 list-disc list-inside mb-1">$1</li>')
                    // Remove any remaining markdown bold/italic formatting and replace with proper HTML
                    .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
                    // Handle code blocks (though we discourage them in prompts)
                    .replace(/`([^`]+)`/g, '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm">$1</code>')
                    // Wrap paragraphs (but not headings or list items)
                    .replace(/^(?!<h|<li|<p|<div|<strong|<em|<code)(.*?)$/gm, '<p class="mb-2 leading-relaxed">$1</p>');

                explanationContent.innerHTML = formattedExplanation;

                // Scroll to show new content as it streams in
                explanationContent.scrollTop = explanationContent.scrollHeight;

                // Render LaTeX in the explanation
                if (typeof renderMathInElement !== 'undefined') {
                    renderMathInElement(explanationContent, {
                        delimiters: [
                            {left: '$$', right: '$$', display: true},
                            {left: '$', right: '$', display: false},
                            {left: '\\(', right: '\\)', display: false},
                            {left: '\\[', right: '\\]', display: true}
                        ],
                        throwOnError: false,
                        output: 'html'
                    });
                }
            }
        } catch (error) {
            console.error('Error fetching explanation:', error);
            explanationContent.innerHTML = `<p class="text-red-600">Error: ${error.message}</p>`;
        } finally {
            // Hide loading indicator
            loadingIndicator.classList.add('hidden');
        }
    }

    // Modify the existing submitPart function to handle image uploads
    async function submitPart(event, partId) {
        event.preventDefault();
        const form = event.target;

        const submitButton = document.getElementById(`submit-part-${partId}`);
        const loading = document.getElementById(`loading_${partId}`);
        const correctAnswer = document.getElementById(`correct_answer_${partId}`);

        // Disable the submit button and show loading
        submitButton.disabled = true;
        loading.classList.remove('hidden');

        try {
            const formData = new FormData(form);
            formData.append('confidence_level', 'Medium');

            const response = await fetch(form.action, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            // Show feedback for this part
            const feedbackDiv = document.querySelector(`#feedback_${partId}`);
            if (feedbackDiv && result.feedback) {
                // Add verdict and score display
                const verdictHtml = `
                    <div class="mb-4 flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="text-sm font-medium">Verdict:</span>
                            <span class="text-sm font-semibold ${result.score == result.max_score ? 'text-green-600' : result.score > 0 ? 'text-yellow-600' : 'text-red-600'}">
                                ${result.score == result.max_score ? 'Correct' : result.score > 0 ? 'Partial' : 'Incorrect'}
                            </span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm font-medium">Score:</span>
                            <span class="text-sm font-semibold ${result.score >= result.max_score ? 'text-green-600' : result.score > 0 ? 'text-yellow-600' : 'text-red-600'}">
                                ${result.score}/${result.max_score}
                            </span>
                        </div>
                    </div>
                `;
                feedbackDiv.querySelector('.prose').innerHTML = verdictHtml + result.feedback;
                feedbackDiv.classList.remove('hidden');
            }

            // Show correct answer for this part if the answer was not irrelevant
            if (correctAnswer && !result.feedback.includes('irrelevant')) {
                correctAnswer.classList.remove('hidden');
            }

        } catch (error) {
            console.error('Error submitting answer:', error);
            // Display the error message from the caught error
            alert('An error occurred: ' + error.message);
        } finally {
            // Hide loading and re-enable submit button
            loading.classList.add('hidden');
            submitButton.disabled = false;
        }

        return false;
    }

    function toggleCorrectAnswer(partId) {
        const answerContent = document.getElementById(`answer_content_${partId}`);
        const arrow = document.getElementById(`arrow_${partId}`);
        const button = arrow.parentElement;

        if (answerContent.classList.contains('hidden')) {
            answerContent.classList.remove('hidden');
            arrow.classList.add('rotate-180');
            button.querySelector('span').textContent = 'Hide Correct Answer';
        } else {
            answerContent.classList.add('hidden');
            arrow.classList.remove('rotate-180');
            button.querySelector('span').textContent = 'Show Correct Answer';
        }
    }

    // Function to toggle correct answer visibility for marking points
    function toggleCorrectAnswerVisibility(container) {
        container.classList.toggle('revealed');
    }

    // Function to toggle feedback visibility
    function toggleFeedbackVisibility(container) {
        container.classList.toggle('revealed');
    }

    async function extractMarkingPoints(partId) {
        try {
            const response = await fetch(`/extract_marking_points/${partId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();

            if (data.status === 'success') {
                // Refresh the page to show new marking points
                window.location.reload();
            } else {
                alert('Error extracting marking points: ' + data.message);
            }
        } catch (error) {
            alert('Error extracting marking points: ' + error.message);
        }
    }

    async function updateMarkingPoint(markingPointId, field, value) {
        try {
            const response = await fetch(`/update_marking_point/${markingPointId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    [field]: value
                })
            });

            const data = await response.json();

            if (data.status !== 'success') {
                alert('Error updating marking point: ' + data.message);
            }
        } catch (error) {
            alert('Error updating marking point: ' + error.message);
        }
    }

    async function deleteMarkingPoint(markingPointId) {
        if (!confirm('Are you sure you want to delete this marking point?')) {
            return;
        }

        try {
            const response = await fetch(`/delete_marking_point/${markingPointId}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.status === 'success') {
                // Remove the marking point element from the DOM
                const element = document.querySelector(`[data-marking-point-id="${markingPointId}"]`);
                if (element) {
                    element.remove();
                }
            } else {
                alert('Error deleting marking point: ' + data.message);
            }
        } catch (error) {
            alert('Error deleting marking point: ' + error.message);
        }
    }

    async function addNewMarkingPoint(partId) {
        try {
            const response = await fetch(`/add_marking_point/${partId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    description: 'New marking point',
                    score: 1.0,
                    order: document.querySelectorAll(`#marking-points-${partId} > div`).length
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                // Refresh the page to show the new marking point
                window.location.reload();
            } else {
                alert('Error adding marking point: ' + data.message);
            }
        } catch (error) {
            alert('Error adding marking point: ' + error.message);
        }
    }

    async function moveMarkingPoint(markingPointId, direction) {
        try {
            const response = await fetch(`/move_marking_point/${markingPointId}/${direction}`, {
                method: 'POST'
            });

            const data = await response.json();

            if (data.status === 'success') {
                // Refresh the page to show updated order
                window.location.reload();
            } else {
                alert('Error moving marking point: ' + data.message);
            }
        } catch (error) {
            alert('Error moving marking point: ' + error.message);
        }
    }

    // Add this function to generate a color for marking points
    function getMarkingPointColor(index) {
        const colors = [
            'bg-green-100 text-green-800',
            'bg-blue-100 text-blue-800',
            'bg-purple-100 text-purple-800',
            'bg-yellow-100 text-yellow-800',
            'bg-indigo-100 text-indigo-800'
        ];
        return colors[index % colors.length];
    }

    // Function to confirm deletion of a question
    function confirmDelete(questionId) {
        // Create a custom modal dialog
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Confirm Deletion</h3>
                <p class="text-gray-600 mb-6">Are you sure you want to delete this question? This action cannot be undone.</p>
                <div class="flex justify-end space-x-3">
                    <button id="cancel-delete" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors">
                        Cancel
                    </button>
                    <button id="confirm-delete" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
                        Delete
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Add event listeners
        document.getElementById('cancel-delete').addEventListener('click', function() {
            document.body.removeChild(modal);
        });

        document.getElementById('confirm-delete').addEventListener('click', function() {
            window.location.href = `/delete_question/${questionId}`;
        });

        // Close modal when clicking outside
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    async function submitAnswer(event, partId, questionId) {
        event.preventDefault();
        const form = event.target;
        const inputType = form.getAttribute('data-input-type') || 'saq';

        let answer = '';
        let hasImage = false;

        // Handle different input types
        if (inputType === 'saq') {
            const answerInput = form.querySelector('textarea[name="answer"]');
            const imageInput = form.querySelector('input[type="file"]');

            answer = answerInput ? answerInput.value.trim() : '';
            hasImage = imageInput && imageInput.files && imageInput.files.length > 0;

            if (!answer && !hasImage) {
                alert('Please enter an answer or upload an image before submitting.');
                return false;
            }
        } else if (inputType === 'mcq') {
            const selectedOption = form.querySelector('input[name="answer"]:checked');

            if (!selectedOption) {
                alert('Please select an answer option before submitting.');
                return false;
            }

            answer = selectedOption.value;
        }

        const submitButton = document.getElementById(`submit-part-${partId}`);
        const loading = document.getElementById(`loading_${partId}`);
        const panelsDiv = document.getElementById(`answer_panels_${partId}`);

        // Disable the submit button and show loading
        submitButton.disabled = true;
        loading.classList.remove('hidden');

        try {
            // Create FormData to handle both text and file uploads
            const formData = new FormData();

            if (answer) {
                formData.append('answer', answer);
            }

            if (hasImage) {
                formData.append('image', imageInput.files[0]);
            }

            // Add confidence level if it exists
            const confidenceSelect = form.querySelector('select[name="confidence_level"]');
            if (confidenceSelect) {
                formData.append('confidence_level', confidenceSelect.value);
            }

            const response = await fetch(form.action, {
                method: 'POST',
                body: formData
                // Don't set Content-Type header - browser will set it with boundary for multipart/form-data
            });

            // --- START Rate Limit Check ---
            if (response.status === 429) {
                const errorData = await response.json();
                alert(errorData.message || 'Rate limit exceeded. Please wait and try again.'); // Show alert for rate limit
                // Optionally, re-enable the button or provide specific UI feedback here
                return false; // Stop further processing
            }
            // --- END Rate Limit Check ---

            // Check for other non-OK responses
            if (!response.ok) {
                 // Try to get error message from JSON, otherwise use status text
                let errorMessage = `HTTP error! Status: ${response.status}`;
                try {
                    const errorJson = await response.json();
                    errorMessage = errorJson.message || errorMessage;
                } catch (e) {
                    // Ignore if response is not JSON
                }
                throw new Error(errorMessage);
            }

            const data = await response.json();

            // Debug: Log timing data
            console.log('MCQ Response data:', data);
            console.log('MCQ Timing data:', data.timing);

            // Original success logic starts here (assuming data.status exists for success)
            if (data.status === 'success') {
                // Update the UI with the feedback
                submitButton.disabled = false;
                loading.classList.add('hidden');

                const inputType = form.getAttribute('data-input-type') || 'saq';

                // Clear any previous scoring content for this part and show the panels
                panelsDiv.innerHTML = `
                    <div class="grid grid-cols-2 gap-6">
                        <div class="p-5 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Your Answer</h4>
                            <div id="user_answer_${partId}" class="text-sm text-gray-700 space-y-3"></div>
                        </div>
                        <div class="p-5 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Marking Points</h4>
                            <div id="marking_scheme_${partId}" class="text-sm text-gray-700 space-y-3"></div>
                        </div>
                    </div>

                    <!-- Explain Answer Button -->
                    <div class="mt-4 flex justify-center">
                        <button type="button" onclick="explainAnswer(${partId}, ${questionId})" class="explain-button inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-lightbulb mr-1.5"></i> Explain Answer
                        </button>
                    </div>

                    <!-- Explanation Section -->
                    <div id="explanation-${partId}" class="explanation-container mt-4 hidden">
                        <div class="p-5 bg-white rounded-lg border border-gray-200 shadow-sm">
                            <div class="flex items-center justify-between mb-3">
                                <h4 class="text-sm font-semibold text-gray-900">Key Concepts & Explanation (Based off RI notes)</h4>
                                <div class="explanation-loading-${partId} hidden">
                                    <div class="w-5 h-5 border-2 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
                                </div>
                            </div>
                            <div class="explanation-content-${partId} prose prose-sm max-w-none text-gray-700 latex-content leading-relaxed">
                                <!-- Explanation content will be loaded here -->
                            </div>
                        </div>
                    </div>
                `;
                panelsDiv.classList.remove('hidden');

                // Get the panels
                const userAnswerDiv = document.getElementById(`user_answer_${partId}`);
                const markingSchemeDiv = document.getElementById(`marking_scheme_${partId}`);

                if (inputType === 'mcq') {
                    // For MCQ, show a simple feedback message
                    // Update the user answer panel
                    if (userAnswerDiv) {
                        const selectedOption = form.querySelector(`input[name="answer"]:checked`);
                        const optionLabel = selectedOption ?
                            selectedOption.closest('.mcq-option').querySelector('.option-content').innerHTML :
                            'No option selected';

                        userAnswerDiv.innerHTML = `
                            <div class="whitespace-pre-wrap">
                                <p class="mb-2">You selected:</p>
                                <div class="p-3 border rounded-md ${data.is_correct ? 'bg-green-50 border-green-300' : 'bg-red-50 border-red-300'}">
                                    ${optionLabel}
                                </div>
                            </div>
                            <div class="mt-4 text-sm ${data.is_correct ? 'text-green-600 font-medium' : 'text-red-600 font-medium'}">
                                ${data.feedback}
                            </div>
                            <div class="mt-2 text-sm text-gray-500">Score: ${data.score}/${data.max_score}</div>
                            ${data.timing ?
                                '<div class="mt-3 pt-3 border-t border-gray-100">' +
                                    '<div class="flex items-center justify-between mb-2">' +
                                        '<span class="text-xs text-gray-500">Performance</span>' +
                                        '<button type="button" onclick="toggleTimingDetails(' + partId + ', event)" class="text-xs text-gray-400 hover:text-gray-600 focus:outline-none transition-colors">' +
                                            '<span id="timing-toggle-mcq-' + partId + '">⏱ ' + data.timing.total_duration_ms + 'ms</span>' +
                                        '</button>' +
                                    '</div>' +
                                    '<div id="timing-details-mcq-' + partId + '" class="hidden mt-2 space-y-1">' +
                                        data.timing.steps.map((step, index) =>
                                            '<div class="bg-gray-50 rounded p-2 text-xs">' +
                                                '<div class="flex items-center justify-between">' +
                                                    '<span class="text-gray-600">' + step.name + '</span>' +
                                                    '<span class="text-gray-500">' + step.duration_ms + 'ms</span>' +
                                                '</div>' +
                                                '<div class="mt-1 bg-gray-200 rounded-full h-1">' +
                                                    '<div class="bg-gray-400 h-1 rounded-full" style="width: ' + ((step.duration_ms / data.timing.total_duration_ms) * 100) + '%"></div>' +
                                                '</div>' +
                                            '</div>'
                                        ).join('') +
                                    '</div>' +
                                '</div>'
                                : ''}
                        `;
                    }

                    // For MCQ, we don't show detailed marking points
                    if (markingSchemeDiv) {
                        markingSchemeDiv.innerHTML = `
                            <div class="text-sm text-gray-500">
                                ${data.is_correct ?
                                    '<div class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i> Correct answer selected</div>' :
                                    '<div class="flex items-center"><i class="fas fa-times text-red-500 mr-2"></i> Incorrect answer selected</div>'}
                            </div>
                        `;
                    }
                } else {
                    // For SAQ, show the detailed feedback
                    // --- START MODIFIED CODE ---
                    // Display the user's answer with highlights (HTML provided by backend)
                    userAnswerDiv.innerHTML = data.answer;
                    // --- END MODIFIED CODE ---

                    // Display the marking scheme with aligned points and vertical color bar indicators
                    markingSchemeDiv.innerHTML = data.marking_points.map((mp, index) => {
                        // --- START REVISED CODE ---
                        // mp.color now holds the border class (e.g., 'border-yellow-400')
                        const borderClass = mp.color || '';
                        // --- END REVISED CODE ---

                    return `
                    <div class="grid-row flex items-start p-3 rounded bg-gray-50 h-full transform transition-all duration-300 hover:bg-gray-100 hover:shadow-sm">
                        <!-- Vertical Color Bar -->
                        <div class="flex-shrink-0 w-1 self-stretch rounded-l-md mr-3 ${borderClass ? borderClass.replace('border-', 'bg-').replace('-400', '-300') : 'bg-transparent'}"></div>

                        <div class="flex-grow flex items-start space-x-3">
                            <!-- Achieved/Not Achieved Icon -->
                            <div class="flex-shrink-0 mt-0.5">
                                <span class="flex items-center justify-center h-5 w-5 rounded-full ${mp.achieved ? 'bg-green-100 text-green-600' : mp.partial ? 'bg-yellow-100 text-yellow-600' : 'bg-gray-100 text-gray-400'}">
                                    ${mp.achieved ?
                                        '<svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>' :
                                        mp.partial ?
                                        '<svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586L7.707 9.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z" clip-rule="evenodd"></path></svg>' :
                                        '<svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12z" clip-rule="evenodd"></path></svg>'
                                    }
                                </span>
                            </div>
                            <!-- Marking Point Text -->
                            <div class="flex-1">
                                <p class="text-sm text-gray-900 font-medium">
                                    [${mp.achieved_score}/${mp.score} marks]
                                    ${mp.partial ? '<span class="ml-1 text-xs text-yellow-600 font-medium">(Partial Credit)</span>' : ''}
                                </p>
                                ${mp.feedback ? `
                                    <div class="saq-feedback-container relative group mt-2" onclick="toggleFeedbackVisibility(this)">
                                        <div class="saq-feedback-hidden text-sm font-medium ${mp.achieved ? 'text-green-700' : mp.partial ? 'text-yellow-700' : 'text-red-700'} bg-white px-3 py-2 rounded border-l-4 ${mp.achieved ? 'border-green-400' : mp.partial ? 'border-yellow-400' : 'border-red-400'} transition-all duration-300">
                                            <i class="fas ${mp.achieved ? 'fa-check-circle' : mp.partial ? 'fa-exclamation-triangle' : 'fa-times-circle'} mr-2"></i>
                                            <span class="feedback-content">${mp.feedback}</span>
                                        </div>
                                        <div class="saq-hover-hint absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-90 rounded-md opacity-100 transition-opacity duration-300 pointer-events-none">
                                            <div class="flex items-center space-x-2 text-xs text-gray-500">
                                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                                </svg>
                                                <span>Click to reveal feedback</span>
                                            </div>
                                        </div>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>`;
                }).join('');


                // Add script to ensure heights match
                setTimeout(() => {
                    // Render LaTeX in the marking scheme
                    const markingSchemeDiv = document.getElementById(`marking_scheme_${partId}`);
                    if (markingSchemeDiv) {
                        renderLatexInElement(markingSchemeDiv);
                    }

                    const gridRows = document.querySelectorAll('.grid-row');
                    const rows = Array.from(gridRows);
                    const numPoints = data.marking_points.length;

                    // Match heights for each pair of marking points
                    for (let i = 0; i < numPoints; i++) {
                        const leftRow = rows[i];
                        const rightRow = rows[i + numPoints];
                        if (leftRow && rightRow) {
                            const maxHeight = Math.max(leftRow.offsetHeight, rightRow.offsetHeight);
                            leftRow.style.height = maxHeight + 'px';
                            rightRow.style.height = maxHeight + 'px';
                        }
                    }

                    // Add a subtle animation to show the total score
                    const totalScore = data.marking_points.reduce((sum, mp) => sum + (mp.achieved_score || 0), 0);
                    const maxScore = data.marking_points.reduce((sum, mp) => sum + mp.score, 0);

                    // Create and append the score summary element
                    const scoreSummary = document.createElement('div');
                    scoreSummary.className = 'mt-6 p-4 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg shadow-sm opacity-0 transform translate-y-4 transition-all duration-500';
                    scoreSummary.innerHTML = `
                        <div class="flex items-center justify-between">
                            <h3 class="text-sm font-medium text-gray-900">Your Score</h3>
                            <div class="text-lg font-bold ${totalScore === maxScore ? 'text-green-600' : totalScore > 0 ? 'text-amber-600' : 'text-red-600'}">
                                ${totalScore} / ${maxScore}
                            </div>
                        </div>
                        <div class="mt-3 relative h-2 bg-gray-200 rounded-full overflow-hidden">
                            <div class="absolute top-0 left-0 h-full bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full" style="width: ${(totalScore / maxScore) * 100}%; transition: width 1s ease-out;"></div>
                        </div>
                        <div class="mt-3 text-xs text-gray-500 text-center">
                            ${totalScore === maxScore ?
                                'Perfect score! Excellent work!' :
                                totalScore > maxScore * 0.7 ?
                                'Great job! You\'re doing well.' :
                                totalScore > 0 ?
                                'Keep practicing, you\'re making progress.' :
                                'Don\'t give up! Try reviewing the concepts again.'}
                        </div>
                    `;

                    // Append to the parent element
                    const parent = document.getElementById(`answer_panels_${partId}`);
                    if (parent) {
                        parent.appendChild(scoreSummary);

                        // Add timing information if available
                        if (data.timing) {
                            const timingDiv = document.createElement('div');
                            timingDiv.className = 'mt-4 pt-3 border-t border-gray-100 opacity-0 transform translate-y-2 transition-all duration-300';
                            timingDiv.innerHTML =
                                '<div class="flex items-center justify-between mb-2">' +
                                    '<span class="text-xs text-gray-500">Performance</span>' +
                                    '<button type="button" onclick="toggleTimingDetails(' + partId + ', event)" class="text-xs text-gray-400 hover:text-gray-600 focus:outline-none transition-colors">' +
                                        '<span id="timing-toggle-saq-' + partId + '">⏱ ' + data.timing.total_duration_ms + 'ms</span>' +
                                    '</button>' +
                                '</div>' +
                                '<div id="timing-details-saq-' + partId + '" class="hidden mt-2 space-y-1">' +
                                    data.timing.steps.map((step, index) =>
                                        '<div class="bg-gray-50 rounded p-2 text-xs">' +
                                            '<div class="flex items-center justify-between">' +
                                                '<span class="text-gray-600">' + step.name + '</span>' +
                                                '<span class="text-gray-500">' + step.duration_ms + 'ms</span>' +
                                            '</div>' +
                                            '<div class="mt-1 bg-gray-200 rounded-full h-1">' +
                                                '<div class="bg-gray-400 h-1 rounded-full" style="width: ' + ((step.duration_ms / data.timing.total_duration_ms) * 100) + '%"></div>' +
                                            '</div>' +
                                        '</div>'
                                    ).join('') +
                                '</div>';

                            parent.appendChild(timingDiv);

                            // Trigger animation for timing div after a delay
                            setTimeout(() => {
                                timingDiv.classList.remove('opacity-0', 'translate-y-2');
                            }, 400);
                        }

                        // Trigger animation after a short delay
                        setTimeout(() => {
                            scoreSummary.classList.remove('opacity-0', 'translate-y-4');
                        }, 300);
                    }
                }, 100);
                } // Close the else block for SAQ

            // Removed the 'else' part here as non-success JSON was handled by !response.ok check above
            } else {
              //alert('Error: ' + data.message); // This might be redundant now
            }
        } catch (error) {
            console.error('Error submitting answer:', error);
            // Display the error message from the caught error
            alert('An error occurred: ' + error.message);
        } finally {
            // Hide loading and re-enable submit button
            loading.classList.add('hidden');
            submitButton.disabled = false;
        }

        return false;
    }
</script>

<style>
    /* Enhanced animations */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .animate-fade-in {
        animation: fadeIn 0.5s ease-out forwards;
    }

    .animate-pulse-slow {
        animation: pulse 2s ease-in-out infinite;
    }

    /* Enhanced evidence text styling */
    .evidence-text {
        position: relative;
        border-left: 3px solid currentColor;
        transition: all 0.2s ease;
    }

    .evidence-text:hover {
        transform: translateX(2px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    /* Improved grid row styling */
    .grid-row {
        position: relative;
        overflow: hidden;
    }

    .grid-row::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background: linear-gradient(to right, transparent, rgba(99, 102, 241, 0.3), transparent);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .grid-row:hover::after {
        transform: scaleX(1);
    }

    /* Enhanced form elements */
    textarea:focus {
        box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
        transform: translateY(-1px);
    }

    button {
        position: relative;
        overflow: hidden;
    }

    button::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 5px;
        height: 5px;
        background: rgba(255, 255, 255, 0.7);
        opacity: 0;
        border-radius: 100%;
        transform: scale(1, 1) translate(-50%);
        transform-origin: 50% 50%;
    }

    button:focus:not(:active)::after {
        animation: ripple 0.6s ease-out;
    }

    @keyframes ripple {
        0% {
            transform: scale(0, 0);
            opacity: 0.5;
        }
        100% {
            transform: scale(20, 20);
            opacity: 0;
        }
    }
    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .grid-cols-12 {
            grid-template-columns: 1fr;
        }

        .col-span-9, .col-span-3 {
            grid-column: span 12 / span 12;
        }

        .hidden-mobile {
            display: none;
        }

        .px-4 {
            padding-left: 0.75rem;
            padding-right: 0.75rem;
        }

        .prose img {
            max-width: 100%;
            height: auto;
        }

        .text-lg {
            font-size: 1.1rem;
        }

        .flex-col-mobile {
            flex-direction: column;
        }

        .w-full-mobile {
            width: 100%;
        }

        .mt-4-mobile {
            margin-top: 1rem;
        }

        /* Adjust form elements for touch */
        input, textarea, button, select {
            font-size: 16px; /* Prevents iOS zoom on focus */
            min-height: 44px; /* Better touch targets */
        }

        /* Ensure math elements are properly sized */
        .mathquill-editable {
            max-width: 100%;
            overflow-x: auto;
        }

        /* Improve spacing for mobile */
        .mb-6 {
            margin-bottom: 1rem;
        }

        .p-6 {
            padding: 1rem;
        }
    }
</style>

<script>
    // Define the submitAllAnswers function directly in the HTML
    async function submitAllAnswers(event) {
        event.preventDefault();
        console.log("submitAllAnswers called");
        const submitAllButton = document.getElementById('submit-all-button');
        if (!submitAllButton) {
            console.error("Submit all button not found");
            return;
        }

        submitAllButton.disabled = true;

        // Find all forms with data-part-id attribute
        const forms = document.querySelectorAll('form[data-part-id]');
        console.log(`Found ${forms.length} forms to submit`);

        if (forms.length === 0) {
            alert("No answer forms found to submit");
            submitAllButton.disabled = false;
            return;
        }

        // Show all loading indicators
        forms.forEach(form => {
            const partId = form.getAttribute('data-part-id');
            const loading = document.getElementById(`loading_${partId}`);
            if (loading) loading.classList.remove('hidden');
        });

        try {
            // Create array of promises for all form submissions
            const submissionPromises = Array.from(forms).map(async form => {
                const partId = form.getAttribute('data-part-id');
                const inputType = form.getAttribute('data-input-type') || 'saq';
                // Extract question ID from form action URL
                const questionId = form.action.match(/question_id=(\d+)/)[1];
                let answer = '';

                if (inputType === 'saq') {
                    const textarea = form.querySelector('textarea[name="answer"]');

                    if (!textarea) {
                        console.error(`No textarea found for part ${partId}`);
                        return null;
                    }

                    answer = textarea.value.trim();
                    if (!answer) {
                        console.log(`No answer provided for part ${partId}, skipping`);
                        return null;
                    }
                } else if (inputType === 'mcq') {
                    const selectedOption = form.querySelector('input[name="answer"]:checked');

                    if (!selectedOption) {
                        console.log(`No option selected for MCQ part ${partId}, skipping`);
                        return null;
                    }

                    answer = selectedOption.value;
                } else {
                    console.error(`Unknown input type: ${inputType} for part ${partId}`);
                    return null;
                }

                console.log(`Submitting answer for part ${partId}`);

                try {
                    // Create a new FormData object
                    const formData = new FormData();
                    formData.append('answer', answer);
                    formData.append('confidence_level', 'Medium');

                    // Get any file inputs
                    const fileInput = form.querySelector('input[type="file"]');
                    if (fileInput && fileInput.files.length > 0) {
                        formData.append('image', fileInput.files[0]);
                    }

                    const response = await fetch(form.action, {
                        method: 'POST',
                        body: formData
                    });

                    if (!response.ok) {
                        const errorText = await response.text();
                        console.error(`Error response for part ${partId}:`, errorText);
                        return {
                            partId,
                            error: `HTTP error ${response.status}`,
                            result: null
                        };
                    }

                    const result = await response.json();
                    console.log(`Received result for part ${partId}:`, result);

                    return {
                        partId,
                        result
                    };
                } catch (error) {
                    console.error(`Error submitting part ${partId}:`, error);
                    return {
                        partId,
                        error: error.message,
                        result: null
                    };
                }
            });

            // Wait for all submissions to complete
            console.log("Waiting for all submissions to complete...");
            const results = await Promise.allSettled(submissionPromises);
            let totalSubmitted = 0;
            let errors = 0;

            // Process all results
            results.forEach(submission => {
                if (submission.status === 'rejected') {
                    console.error("Promise rejected:", submission.reason);
                    errors++;
                    return;
                }

                if (!submission.value || submission.value.error) {
                    if (submission.value) {
                        console.error("Submission error:", submission.value.error);
                        errors++;
                    }
                    return;
                }

                const { partId, result } = submission.value;
                if (!result) return;

                // Debug: Log timing data for bulk submissions
                console.log('Bulk submission result for part', partId, ':', result);
                console.log('Bulk submission timing data for part', partId, ':', result.timing);

                // Handle the answer panels if they exist
                const panelsDiv = document.getElementById(`answer_panels_${partId}`);
                if (panelsDiv && result.status === 'success') {
                    // Clear any previous scoring content for this part and show the panels
                    panelsDiv.innerHTML = `
                        <div class="grid grid-cols-2 gap-6">
                            <div class="p-5 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                                <h4 class="text-sm font-medium text-gray-900 mb-3">Your Answer</h4>
                                <div id="user_answer_${partId}" class="text-sm text-gray-700 space-y-3"></div>
                            </div>
                            <div class="p-5 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                                <h4 class="text-sm font-medium text-gray-900 mb-3">Marking Points</h4>
                                <div id="marking_scheme_${partId}" class="text-sm text-gray-700 space-y-3"></div>
                            </div>
                        </div>

                        <!-- Explain Answer Button -->
                        <div class="mt-4 flex justify-center">
                            <button type="button" onclick="explainAnswer(${partId}, ${questionId})" class="explain-button inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <i class="fas fa-lightbulb mr-1.5"></i> Explain Answer
                            </button>
                        </div>

                        <!-- Explanation Section -->
                        <div id="explanation-${partId}" class="explanation-container mt-4 hidden">
                            <div class="p-5 bg-white rounded-lg border border-gray-200 shadow-sm">
                                <div class="flex items-center justify-between mb-3">
                                    <h4 class="text-sm font-semibold text-gray-900">Key Concepts & Explanation (Based off RI notes)</h4>
                                    <div class="explanation-loading-${partId} hidden">
                                        <div class="w-5 h-5 border-2 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
                                    </div>
                                </div>
                                <div class="explanation-content-${partId} prose prose-sm max-w-none text-gray-700 latex-content leading-relaxed">
                                    <!-- Explanation content will be loaded here -->
                                </div>
                            </div>
                        </div>
                    `;
                    panelsDiv.classList.remove('hidden');

                    // Get the panels
                    const userAnswerDiv = document.getElementById(`user_answer_${partId}`);
                    const markingSchemeDiv = document.getElementById(`marking_scheme_${partId}`);

                    if (userAnswerDiv && result.answer) {
                        userAnswerDiv.innerHTML = result.answer;
                    }

                    if (markingSchemeDiv && result.marking_points) {
                        // Display the marking scheme
                        markingSchemeDiv.innerHTML = result.marking_points.map((mp, index) => {
                            const borderClass = mp.color || '';
                            return `
                            <div class="grid-row flex items-start p-3 rounded bg-gray-50 h-full transform transition-all duration-300 hover:bg-gray-100 hover:shadow-sm">
                                <!-- Vertical Color Bar -->
                                <div class="flex-shrink-0 w-1 self-stretch rounded-l-md mr-3 ${borderClass ? borderClass.replace('border-', 'bg-').replace('-400', '-300') : 'bg-transparent'}"></div>

                                <div class="flex-grow flex items-start space-x-3">
                                    <!-- Achieved/Not Achieved Icon -->
                                    <div class="flex-shrink-0 mt-0.5">
                                        <span class="flex items-center justify-center h-5 w-5 rounded-full ${mp.achieved ? 'bg-green-100 text-green-600' : mp.partial ? 'bg-yellow-100 text-yellow-600' : 'bg-gray-100 text-gray-400'}">
                                            ${mp.achieved ?
                                                '<svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>' :
                                                mp.partial ?
                                                '<svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586L7.707 9.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z" clip-rule="evenodd"></path></svg>' :
                                                '<svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12z" clip-rule="evenodd"></path></svg>'
                                            }
                                        </span>
                                    </div>
                                    <!-- Marking Point Text -->
                                    <div class="flex-1">
                                        <p class="text-sm font-medium ${mp.achieved ? 'text-green-800' : mp.partial ? 'text-yellow-800' : 'text-red-800'}">
                                            [${mp.achieved_score}/${mp.score} marks]
                                            ${mp.partial ? '<span class="ml-1 text-xs text-yellow-600 font-medium">(Partial Credit)</span>' : ''}
                                        </p>
                                        ${mp.feedback ? `
                                            <div class="saq-feedback-container relative group mt-2" onclick="toggleFeedbackVisibility(this)">
                                                <div class="saq-feedback-hidden text-sm font-medium ${mp.achieved ? 'text-green-700' : mp.partial ? 'text-yellow-700' : 'text-red-700'} bg-white px-3 py-2 rounded border-l-4 ${mp.achieved ? 'border-green-400' : mp.partial ? 'border-yellow-400' : 'border-red-400'} transition-all duration-300">
                                                    <i class="fas ${mp.achieved ? 'fa-check-circle' : mp.partial ? 'fa-exclamation-triangle' : 'fa-times-circle'} mr-2"></i>
                                                    <span class="feedback-content">${mp.feedback}</span>
                                                </div>
                                                <div class="saq-hover-hint absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-90 rounded-md opacity-100 transition-opacity duration-300 pointer-events-none">
                                                    <div class="flex items-center space-x-2 text-xs text-gray-500">
                                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                                        </svg>
                                                        <span>Click to reveal feedback</span>
                                                    </div>
                                                </div>
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                            </div>`;
                        }).join('');
                    }

                    totalSubmitted++;

                    // Render LaTeX in the marking points
                    setTimeout(() => {
                        try {
                            console.log(`Rendering LaTeX in marking points for part ${partId}`);

                            // Render LaTeX in the entire marking scheme div
                            const markingSchemeDiv = document.getElementById(`marking_scheme_${partId}`);
                            console.log(`Found marking scheme div:`, markingSchemeDiv);
                            if (markingSchemeDiv) {
                                console.log(`Marking scheme div content:`, markingSchemeDiv.innerHTML.substring(0, 200));
                                renderLatexInElement(markingSchemeDiv);
                            } else {
                                console.warn(`No marking scheme div found for part ${partId}`);
                            }

                            console.log(`LaTeX rendering complete in marking points for part ${partId}`);
                        } catch (error) {
                            console.error("Error rendering LaTeX in marking points:", error);
                        }
                    }, 100);
                }

                // Handle the feedback div if it exists
                const feedbackDiv = document.getElementById(`feedback-${partId}`);
                if (feedbackDiv && result.status === 'success') {
                    // Generate detailed feedback HTML based on marking points
                    let feedbackHtml = `
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Feedback Breakdown</h4>
                        <div class="space-y-3">
                    `;

                    if (result.marking_points && result.marking_points.length > 0) {
                        result.marking_points.forEach(mp => {
                            feedbackHtml += `
                                <div class="flex items-start space-x-3 p-3 rounded ${mp.achieved ? 'bg-green-50' : mp.partial ? 'bg-yellow-50' : 'bg-red-50'} border ${mp.achieved ? 'border-green-200' : mp.partial ? 'border-yellow-200' : 'border-red-200'}">
                                    <div class="flex-shrink-0 pt-1">
                                        ${mp.achieved
                                            ? '<svg class="h-5 w-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/></svg>'
                                            : mp.partial
                                            ? '<svg class="h-5 w-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586L7.707 9.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z"/></svg>'
                                            : '<svg class="h-5 w-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/></svg>'
                                        }
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium ${mp.achieved ? 'text-green-800' : mp.partial ? 'text-yellow-800' : 'text-red-800'} mb-2">
                                            [${mp.achieved_score}/${mp.score} marks]
                                            ${mp.partial ? '<span class="ml-1 text-xs text-yellow-600 font-medium">(Partial Credit)</span>' : ''}
                                        </p>
                                        <div class="saq-correct-answer-container relative group" onclick="toggleCorrectAnswerVisibility(this)">
                                            <div class="saq-correct-answer-hidden text-sm ${mp.achieved ? 'text-green-800' : mp.partial ? 'text-yellow-800' : 'text-red-800'} transition-all duration-300 latex-content">${mp.description}</div>
                                            <div class="saq-hover-hint absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-90 rounded-md opacity-100 transition-opacity duration-300 pointer-events-none">
                                                <div class="flex items-center space-x-2 text-xs text-gray-500">
                                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                                    </svg>
                                                    <span>Click to reveal answer</span>
                                                </div>
                                            </div>
                                        </div>
                                        ${mp.feedback ? `
                                            <div class="mt-2 text-sm font-medium ${mp.achieved ? 'text-green-700' : mp.partial ? 'text-yellow-700' : 'text-red-700'} bg-white px-3 py-2 rounded border-l-4 ${mp.achieved ? 'border-green-400' : mp.partial ? 'border-yellow-400' : 'border-red-400'}">
                                                <i class="fas ${mp.achieved ? 'fa-check-circle' : mp.partial ? 'fa-exclamation-triangle' : 'fa-times-circle'} mr-2"></i>
                                                <span class="feedback-content">${mp.feedback}</span>
                                            </div>
                                        ` : ''}
                                        ${mp.evidence ? `
                                            <div class="mt-1 text-xs ${mp.achieved ? 'text-green-700' : mp.partial ? 'text-yellow-700' : 'text-red-700'} bg-white bg-opacity-60 p-2 rounded border ${mp.achieved ? 'border-green-200' : mp.partial ? 'border-yellow-200' : 'border-red-200'}">
                                                <strong>Evidence:</strong> "${mp.evidence}"
                                            </div>
                                        ` : (mp.achieved || mp.partial) ? '' : '<p class="mt-1 text-xs text-red-700 italic">No relevant evidence found in answer.</p>'}
                                        ${mp.error ? `<p class="mt-1 text-xs text-red-700 font-semibold">Error during evaluation.</p>` : ''}
                                    </div>
                                </div>
                            `;
                        });
                    } else {
                         feedbackHtml += `<p class="text-sm text-gray-600 italic">No marking points defined for this part.</p>`;
                    }

                    feedbackHtml += `</div>`; // Close space-y-3

                    // Add total score summary
                    const totalScore = result.score || 0;
                    const maxScore = result.max_score || 0;

                    feedbackHtml += `
                        <div class="mt-4 pt-4 border-t border-gray-200 flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-900">Total Score:</span>
                            <span class="text-lg font-bold ${totalScore === maxScore ? 'text-green-600' : totalScore > 0 ? 'text-yellow-600' : 'text-red-600'}">
                                ${totalScore} / ${maxScore}
                            </span>
                        </div>
                    `;

                    // Add timing information if available
                    if (result.timing) {
                        feedbackHtml +=
                            '<div class="mt-3 pt-3 border-t border-gray-100">' +
                                '<div class="flex items-center justify-between mb-2">' +
                                    '<span class="text-xs text-gray-500">Performance</span>' +
                                    '<button type="button" onclick="toggleTimingDetails(' + partId + ', event)" class="text-xs text-gray-400 hover:text-gray-600 focus:outline-none transition-colors">' +
                                        '<span id="timing-toggle-' + partId + '">⏱ ' + result.timing.total_duration_ms + 'ms</span>' +
                                    '</button>' +
                                '</div>' +
                                '<div id="timing-details-' + partId + '" class="hidden mt-2 space-y-1">' +
                                    result.timing.steps.map((step, index) =>
                                        '<div class="bg-gray-50 rounded p-2 text-xs">' +
                                            '<div class="flex items-center justify-between">' +
                                                '<span class="text-gray-600">' + step.name + '</span>' +
                                                '<span class="text-gray-500">' + step.duration_ms + 'ms</span>' +
                                            '</div>' +
                                            '<div class="mt-1 bg-gray-200 rounded-full h-1">' +
                                                '<div class="bg-gray-400 h-1 rounded-full" style="width: ' + ((step.duration_ms / result.timing.total_duration_ms) * 100) + '%"></div>' +
                                            '</div>' +
                                        '</div>'
                                    ).join('') +
                                '</div>' +
                            '</div>';
                    }

                    feedbackDiv.innerHTML = feedbackHtml;
                    feedbackDiv.classList.remove('hidden');

                    // Render LaTeX in the feedback
                    setTimeout(() => {
                        try {
                            console.log(`Rendering LaTeX in feedback for part ${partId}`);

                            // Render LaTeX in the entire feedback div
                            const feedbackDiv = document.getElementById(`feedback-${partId}`);
                            console.log(`Found feedback div:`, feedbackDiv);
                            if (feedbackDiv) {
                                console.log(`Feedback div content:`, feedbackDiv.innerHTML.substring(0, 200));
                                renderLatexInElement(feedbackDiv);
                            } else {
                                console.warn(`No feedback div found for part ${partId}`);
                            }

                            console.log(`LaTeX rendering complete in feedback for part ${partId}`);
                        } catch (error) {
                            console.error("Error rendering LaTeX in feedback:", error);
                        }
                    }, 100);

                    // Update background based on overall score
                    if (totalScore === maxScore) {
                        feedbackDiv.className = 'mt-6 rounded-xl p-6 transform transition-all duration-300 bg-green-50 border border-green-200';
                    } else if (totalScore > 0) {
                        feedbackDiv.className = 'mt-6 rounded-xl p-6 transform transition-all duration-300 bg-yellow-50 border border-yellow-200';
                    } else {
                        feedbackDiv.className = 'mt-6 rounded-xl p-6 transform transition-all duration-300 bg-red-50 border border-red-200';
                    }
                }
            });

            console.log(`Submitted ${totalSubmitted} answers with ${errors} errors`);

            if (totalSubmitted > 0) {
                alert(`Successfully submitted ${totalSubmitted} answers!`);
            } else if (errors > 0) {
                alert(`Failed to submit answers. Please check the console for details.`);
            } else {
                alert("No answers were submitted. Please enter answers before submitting.");
            }

        } catch (error) {
            console.error('Error in submitAllAnswers:', error);
            alert('An error occurred while submitting your answers: ' + error.message);
        } finally {
            // Hide all loading indicators
            forms.forEach(form => {
                const partId = form.getAttribute('data-part-id');
                const loading = document.getElementById(`loading_${partId}`);
                if (loading) loading.classList.add('hidden');
            });
            submitAllButton.disabled = false;
        }
    }

    // Check if the JavaScript is loaded
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOMContentLoaded event fired');

        // Check if submitAllAnswers function exists
        if (typeof submitAllAnswers === 'function') {
            console.log('submitAllAnswers function is defined');
            document.getElementById('debug-info').innerHTML = '<p class="text-green-600">JavaScript is properly loaded.</p>';
        } else {
            console.error('submitAllAnswers function is NOT defined');
            document.getElementById('debug-info').innerHTML = '<p class="text-red-600">Error: JavaScript functions are not properly loaded. Please refresh the page or contact support.</p>';
        }

        // Add event listener to the submit-all-button
        const submitAllButton = document.getElementById('submit-all-button');
        if (submitAllButton) {
            console.log('Found submit-all-button, adding event listener');
            submitAllButton.addEventListener('click', function(event) {
                console.log('Submit All button clicked via event listener');
                if (typeof submitAllAnswers === 'function') {
                    submitAllAnswers(event);
                } else {
                    alert('Error: submitAllAnswers function is not defined. Please check the console for more details.');
                }
            });
        } else {
            console.error('submit-all-button not found in the DOM');
        }
    });
</script>

{% endblock %}
